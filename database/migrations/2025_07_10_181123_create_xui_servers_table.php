<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Серверы XUI
         */
        Schema::create('xui_servers', function (Blueprint $table) {
            $table->id()->primary();

            // basic info
            $table->string('name')->nullable();
            $table->string('address')->nullable();
            $table->integer('port')->default(57775);
            $table->string('web_base_path')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->text('notes')->nullable();

            // authentication
            $table->timestamp('last_login_at')->nullable()->comment('Last login to the server to get the session cookie');
            $table->text('session_cookie')->nullable()->comment('3x-ui session cookie');

            $table->boolean('is_active')->default(false)->comment('Whether the server is active and should be used for new subscriptions');
            $table->boolean('auto_sync')->default(false)->comment('Whether the server should be automatically synchronized with the panel by the scheduler');

            // preset data for server
            $table->uuid('system_client_id_for_inbound')->comment('The client ID that should be created on the server for inbounds as a first system client (inbound can not be empty). Autogenerated UUID on creation');

            // storing calculated data
            $table->decimal('server_load', 5, 2)->nullable();
            $table->smallInteger('clients_count')->default(0);
            $table->smallInteger('clients_online_count')->default(0);
            $table->json('clients_online_list')->nullable()->comment('Array of strings (emails) or null');

            // storing raw data from the server
            $table->json('raw_server_status')->nullable()->comment('The raw server status from the API /server/status. Object or parameters');
            $table->json('raw_config')->nullable()->comment('The config from the API /server/getConfigJson. Object or parameters');
            $table->json('raw_settings_all')->nullable()->comment('The settings from the API /panel/setting/all. Object or parameters');
            $table->json('raw_settings_short')->nullable()->comment('The settings from the API /panel/setting/defaultSettings. Object or parameters');
            $table->json('raw_inbounds_list')->nullable()->comment('The inbounds list from the API /list. Array of objects or null');
            $table->json('raw_clients_online')->nullable()->comment('The clients online from the API /onlines. Array of strings or null');

            $table->timestamp('raw_server_status_updated_at')->nullable();
            $table->timestamp('raw_config_updated_at')->nullable();
            $table->timestamp('raw_settings_all_updated_at')->nullable();
            $table->timestamp('raw_settings_short_updated_at')->nullable();
            $table->timestamp('raw_inbounds_list_updated_at')->nullable();
            $table->timestamp('raw_clients_online_updated_at')->nullable();

            $table->timestamp('last_sync_at')->nullable()->comment('Last synchronization with the server, executed by the scheduler');

            $table->timestamps();
            // soft deletes
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('xui_servers');
    }
};
