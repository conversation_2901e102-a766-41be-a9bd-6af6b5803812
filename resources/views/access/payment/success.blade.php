<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background: #4caf50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 40px;
        }
        h1 {
            color: #4caf50;
            margin-bottom: 20px;
        }
        .order-info {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .next-steps {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        .next-steps h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .button {
            display: inline-block;
            background: #2196f3;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px;
            transition: background 0.3s ease;
        }
        .button:hover {
            background: #1976d2;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        
        <h1>Payment Successful!</h1>
        <p>Thank you for your purchase. Your VPN subscription has been processed successfully.</p>

        <div class="order-info">
            <h3>Order Details</h3>
            <div class="info-row">
                <span class="info-label">Order ID:</span>
                <span class="info-value">{{ $order }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">User ID:</span>
                <span class="info-value">{{ $uuid }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Payment Date:</span>
                <span class="info-value">{{ now()->format('M j, Y \a\t g:i A') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value" style="color: #4caf50; font-weight: bold;">Completed</span>
            </div>
        </div>

        <div class="next-steps">
            <h3>What's Next?</h3>
            <ul>
                <li>Your VPN subscription will be activated within a few minutes</li>
                <li>You will receive an email confirmation with your account details</li>
                <li>Download the VPN client and connect using your credentials</li>
                <li>If you have any issues, please contact our support team</li>
            </ul>
        </div>

        @if(session('success'))
            <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
                {{ session('success') }}
            </div>
        @endif

        <div style="margin-top: 30px;">
            <a href="{{ route('vpn.plan.selection', $uuid) }}" class="button secondary">
                View Plans
            </a>
            <a href="#" class="button" onclick="window.close(); return false;">
                Close Window
            </a>
        </div>

        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p>
                <strong>Need Help?</strong><br>
                If you have any questions about your subscription or need technical support, 
                please contact our customer service team.
            </p>
        </div>
    </div>
</body>
</html>
