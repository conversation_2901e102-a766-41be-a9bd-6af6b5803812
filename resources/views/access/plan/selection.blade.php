<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select VPN Plan</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .subscription-status {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 30px;
        }
        .subscription-status.expired {
            background: #ffebee;
            border-color: #f44336;
        }
        .subscription-status.expiring-soon {
            background: #fff3e0;
            border-color: #ff9800;
        }
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .plan-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            border-color: #2196f3;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
        }
        .plan-card.current {
            border-color: #4caf50;
            background: #f1f8e9;
        }
        .plan-card.selected {
            border-color: #2196f3;
            background: #e3f2fd;
        }
        .current-badge {
            position: absolute;
            top: -10px;
            right: 10px;
            background: #4caf50;
            color: white;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .plan-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .plan-price {
            font-size: 24px;
            color: #2196f3;
            margin-bottom: 15px;
        }
        .plan-duration {
            color: #666;
            margin-bottom: 15px;
        }
        .payment-methods {
            margin-top: 30px;
        }
        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .payment-method {
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            border-color: #2196f3;
        }
        .payment-method.selected {
            border-color: #2196f3;
            background: #e3f2fd;
        }
        .purchase-button {
            width: 100%;
            background: #2196f3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .purchase-button:hover {
            background: #1976d2;
        }
        .purchase-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Select Your VPN Plan</h1>
            <p>Choose a subscription plan and payment method to continue</p>
        </div>

        @if($subscriptionStatus['has_subscription'])
            <div class="subscription-status {{ $subscriptionStatus['status'] }}">
                <h3>Current Subscription Status</h3>
                <p>{{ $subscriptionStatus['message'] }}</p>
                @if($currentPlan)
                    <p><strong>Current Plan:</strong> {{ $currentPlan->name }} ({{ $currentPlan->formatted_price }})</p>
                @endif
            </div>
        @endif

        @if($errors->any())
            <div class="error">
                <ul style="margin: 0; padding-left: 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if(session('error'))
            <div class="error">
                {{ session('error') }}
            </div>
        @endif

        <form id="purchase-form" method="POST" action="{{ route('vpn.plan.purchase', $uuid) }}">
            @csrf
            
            <h2>Available Plans</h2>
            <div class="plans-grid">
                @foreach($availablePlans as $plan)
                    <div class="plan-card {{ $currentPlan && $currentPlan->id === $plan->id ? 'current' : '' }}" 
                         data-plan-id="{{ $plan->id }}" 
                         data-price="{{ $plan->price }}">
                        @if($currentPlan && $currentPlan->id === $plan->id)
                            <div class="current-badge">Your Current Plan</div>
                        @endif
                        
                        <div class="plan-name">{{ $plan->name }}</div>
                        <div class="plan-price">{{ $plan->formatted_price }}</div>
                        
                        @if($plan->duration && $plan->duration_units)
                            <div class="plan-duration">
                                Duration: {{ $plan->duration }} {{ $plan->duration_units }}{{ $plan->duration > 1 ? 's' : '' }}
                            </div>
                        @endif
                        
                        @if($plan->traffic_limit_bytes)
                            <div class="plan-duration">
                                Traffic: {{ number_format($plan->traffic_limit_bytes / (1024*1024*1024), 1) }} GB
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <div class="payment-methods">
                <h2>Payment Methods</h2>
                <div class="payment-grid">
                    @foreach($paymentMethods as $method)
                        <div class="payment-method" data-method="{{ $method->code }}">
                            <strong>{{ $method->name }}</strong>
                            @if($method->type)
                                <div style="font-size: 14px; color: #666; margin-top: 5px;">
                                    {{ ucfirst($method->type) }}
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>

            <input type="hidden" name="plan_id" id="selected-plan" value="{{ old('plan_id') }}">
            <input type="hidden" name="payment_method" id="selected-payment" value="{{ old('payment_method') }}">

            <button type="submit" class="purchase-button" id="purchase-btn" disabled>
                Select Plan and Payment Method
            </button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const planCards = document.querySelectorAll('.plan-card');
            const paymentMethods = document.querySelectorAll('.payment-method');
            const selectedPlanInput = document.getElementById('selected-plan');
            const selectedPaymentInput = document.getElementById('selected-payment');
            const purchaseBtn = document.getElementById('purchase-btn');

            let selectedPlan = selectedPlanInput.value;
            let selectedPayment = selectedPaymentInput.value;

            function updateButton() {
                if (selectedPlan && selectedPayment) {
                    purchaseBtn.disabled = false;
                    purchaseBtn.textContent = 'Purchase Selected Plan';
                } else {
                    purchaseBtn.disabled = true;
                    purchaseBtn.textContent = 'Select Plan and Payment Method';
                }
            }

            // Plan selection
            planCards.forEach(card => {
                if (card.dataset.planId === selectedPlan) {
                    card.classList.add('selected');
                }
                
                card.addEventListener('click', function() {
                    planCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedPlan = this.dataset.planId;
                    selectedPlanInput.value = selectedPlan;
                    updateButton();
                });
            });

            // Payment method selection
            paymentMethods.forEach(method => {
                if (method.dataset.method === selectedPayment) {
                    method.classList.add('selected');
                }
                
                method.addEventListener('click', function() {
                    paymentMethods.forEach(m => m.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedPayment = this.dataset.method;
                    selectedPaymentInput.value = selectedPayment;
                    updateButton();
                });
            });

            updateButton();
        });
    </script>
</body>
</html>
