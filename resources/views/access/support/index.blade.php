<!DOCTYPE html>
<html lang="ru">
<head>
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/storage/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/storage/images/favicon/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/storage/images/favicon/favicon-32x32.png">
    <link rel="manifest" href="/storage/images/favicon/site.webmanifest">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Поддержка - SmartVPN</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        #logo {
            background-image: url('/storage/images/smartvpn-logo.jpg');
            background-size: cover;
            background-position: center;
        }

        .mood-button {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            user-select: none;
        }

        .mood-button:hover {
            transform: scale(1.1);
        }

        .mood-button.selected {
            transform: scale(1.25);
            filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
        }

        .mood-button:active {
            transform: scale(0.95);
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .toast.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .toast.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .support-button {
            transition: all 0.2s ease-in-out;
        }

        .support-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .support-button:active {
            transform: translateY(0);
        }

        @media (max-width: 640px) {
            .toast {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
        .fragment {
            position: absolute;
            width: 6px;
            height: 6px;
            background-image: url('/storage/images/smartvpn-logo.jpg');
            background-size: cover;
            background-position: center;
            border-radius: 50%;
            pointer-events: none;
            z-index: 50;
        }
    </style>
</head>
<body class="bg-gray-900 min-h-dvh">
    <div class="min-h-dvh flex justify-center p-4">
        <div class="max-w-md w-full bg-gray-800 rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
                <div class="flex items-center justify-between">
                    <!-- Logo/Avatar -->
                    <div id="logo" class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0 border-2 border-white border-opacity-30"></div>

                    <!-- Status -->
                    <div class="text-right flex-1 ml-4">
                        <p class="text-sm font-medium text-blue-100 mb-1">
                            🔰 ID клиента: <span class="can-copy">{{ $data['client_id'] }}</span>
                        </p>
                        <p class="text-sm font-medium text-white">
                            Подписка <span class="{{ $statusColor }}">{{ $status }}</span>
                            <span id="expiryDate" class="text-blue-100"></span>
                        </p>

                        <p id="remainingTime" class="text-xs text-blue-100 mt-1" style="display: none;"></p>

                        @if($isDemo)
                            <p class="text-xs text-yellow-200 mt-1">
                                <i class="fas fa-star mr-1"></i>Демо версия
                            </p>
                        @endif
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- Rating System -->
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-white mb-3">Как сегодня работает VPN?</h3>
                    <div class="flex justify-center space-x-3 mb-4" id="ratingContainer">
                        <button class="mood-button text-3xl" data-rating="1" data-emoji="😡" data-label="Очень плохо" title="Очень плохо">😡</button>
                        <button class="mood-button text-3xl" data-rating="2" data-emoji="😕" data-label="Плохо" title="Плохо">😕</button>
                        <button class="mood-button text-3xl" data-rating="3" data-emoji="😐" data-label="Нормально" title="Нормально">😐</button>
                        <button class="mood-button text-3xl" data-rating="4" data-emoji="🙂" data-label="Хорошо" title="Хорошо">🙂</button>
                        <button class="mood-button text-3xl" data-rating="5" data-emoji="😄" data-label="Отлично" title="Отлично">😄</button>
                    </div>
                    @if($todayRating)
                        <p class="text-sm text-blue-400">
                            <i class="fas fa-edit mr-1"></i>
                            Сегодня вы поставили: {{ $todayRating->emoji }} {{ $todayRating->label }}
                            <br><span class="text-xs text-gray-400">Можете изменить оценку</span>
                        </p>
                    @endif
                </div>

                <div style="display: none;">
                    Log: <span id="debug"></span>
                </div>

                <!-- Support Buttons -->
                <div class="space-y-3">
                    <a href="{{ $telegramSupport }}"
                        onclick="tryToOpenDeepLink(event, '{{ $telegramSupport }}', '{{ $telegramSupportDeepLink }}')"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="support-button flex items-center justify-center w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg"
                    >
                        <i class="fab fa-telegram-plane mr-3 text-xl"></i>
                        Техподдержка в Telegram
                    </a>

                    <a href="{{ $whatsappSupport }}"
                        onclick="tryToOpenDeepLink(event, '{{ $whatsappSupport }}', '{{ $whatsappSupportDeepLink }}')"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="support-button flex items-center justify-center w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg"
                    >
                        <i class="fab fa-whatsapp mr-3 text-xl"></i>
                        Техподдержка в WhatsApp
                    </a>

                    <a href="{{ $referralLink }}"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="support-button flex items-center justify-center w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fas fa-users mr-3 text-xl"></i>
                        Реферальная программа
                    </a>

                    <a href="/subs/{{ $user->uuid }}/modern?autolaunch=no"
                       class="support-button flex items-center justify-center w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg">
                        <i class="fas fa-arrow-left mr-3 text-xl"></i>
                        Посмотреть настройки
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        // Support page data
        const supportData = {
            status: '{{ $status }}',
            expiryTime: '{{ $earliestExpiry ? $earliestExpiry->utc()->toISOString() : '' }}',
            isExpired: {{ $isExpired ? 'true' : 'false' }},
            isLimited: {{ $isLimited ? 'true' : 'false' }},
            isDemo: {{ $isDemo ? 'true' : 'false' }}
        };

        // Rating system
        const ratingButtons = document.querySelectorAll('.mood-button');
        const userUuid = '{{ $user->uuid }}';
        const todayRating = {{ $todayRating ? $todayRating->rating : 'null' }};

        // Initialize rating buttons
        ratingButtons.forEach(button => {
            button.addEventListener('click', function() {
                const rating = this.dataset.rating;
                const emoji = this.dataset.emoji;
                const label = this.dataset.label;

                // Visual feedback
                ratingButtons.forEach(btn => btn.classList.remove('selected'));
                this.classList.add('selected');

                // Submit rating
                submitRating(rating, emoji, label);
            });
        });

        // Highlight current rating if exists
        if (todayRating) {
            const currentButton = document.querySelector(`[data-rating="${todayRating}"]`);
            if (currentButton) {
                currentButton.classList.add('selected');
            }
        }

        // Submit rating function
        async function submitRating(rating, emoji, label) {
            try {
                const response = await fetch('/support/rating', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        uuid: userUuid,
                        rating: parseInt(rating)
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');

                    // Update status message
                    const ratingContainer = document.getElementById('ratingContainer');
                    let statusMessage = ratingContainer.parentNode.querySelector('.text-blue-400, .text-yellow-400');

                    if (!statusMessage) {
                        statusMessage = document.createElement('p');
                        statusMessage.className = 'text-sm text-blue-400';
                        ratingContainer.parentNode.appendChild(statusMessage);
                    } else {
                        statusMessage.className = 'text-sm text-blue-400';
                    }

                    statusMessage.innerHTML = `
                        <i class="fas fa-edit mr-1"></i>
                        Сегодня вы поставили: ${data.data.emoji} ${data.data.label}
                        <br><span class="text-xs text-gray-400">Можете изменить оценку</span>
                    `;
                } else {
                    showToast(data.message || 'Ошибка при отправке оценки', 'error');
                }
            } catch (error) {
                console.error('Rating submission error:', error);
                showToast('Ошибка при отправке оценки', 'error');
            }
        }

        // Toast notification function
        function showToast(message, type = 'success', duration = 5000) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-3"></i>
                    <span>${message}</span>
                </div>
            `;

            document.getElementById('toastContainer').appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // Format expiry date (same as subscription-modern-simple)
        function formatExpiryDate(isoString) {
            if (!isoString) return '';

            try {
                const date = new Date(isoString);
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');

                return `${day}.${month}.${year} ${hours}:${minutes}`;
            } catch (error) {
                console.error('Error formatting date:', error);
                return '';
            }
        }

        // Calculate remaining time (same as subscription-modern-simple)
        function calculateRemainingTime(isoString) {
            if (!isoString) return null;

            try {
                const expiryDate = new Date(isoString);
                const now = new Date();
                const diffMs = expiryDate.getTime() - now.getTime();

                if (diffMs <= 0) {
                    return { expired: true, display: 'истекла' };
                }

                const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

                if (days > 0) {
                    return { expired: false, display: `${days} дн.` };
                } else if (hours > 0) {
                    return { expired: false, display: `${hours} ч.` + ((minutes) ? ` ${minutes} мин.` : '') };
                } else if (minutes > 0) {
                    return { expired: false, display: `${minutes} мин.` };
                } else {
                    return { expired: false, display: 'менее минуты' };
                }
            } catch (error) {
                console.error('Error calculating remaining time:', error);
                return null;
            }
        }

        // Initialize expiry display (same logic as subscription-modern-simple)
        function initializeExpiryDisplay() {
            const expiryDateElement = document.getElementById('expiryDate');
            const remainingTimeElement = document.getElementById('remainingTime');

            if (supportData.expiryTime) {
                // Format expiry date in user's timezone
                const formattedDate = formatExpiryDate(supportData.expiryTime);

                // Show "до" only if subscription is not expired
                if (supportData.status === 'истекла') {
                    expiryDateElement.textContent = formattedDate;
                } else {
                    expiryDateElement.textContent = `до ${formattedDate}`;
                }

                // Calculate and display remaining time
                const remaining = calculateRemainingTime(supportData.expiryTime);
                if (remaining && !remaining.expired && (supportData.status === 'активна' || supportData.status === 'демо')) {
                    remainingTimeElement.textContent = `осталось ${remaining.display}`;
                    remainingTimeElement.style.display = 'block';
                } else {
                    remainingTimeElement.style.display = 'none';
                }
            }
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize expiry display
            initializeExpiryDisplay();

            // Add loading states to support buttons
            const supportButtons = document.querySelectorAll('.support-button');
            supportButtons.forEach(button => {
                if (button.href && button.href.startsWith('http')) {
                    button.addEventListener('click', function() {
                        const icon = this.querySelector('i');
                        const originalClass = icon.className;
                        icon.className = 'fas fa-spinner fa-spin mr-3 text-xl';

                        setTimeout(() => {
                            icon.className = originalClass;
                        }, 3000);
                    });
                }
            });
        });
    </script>
    <script>
        (function enableCopyTooltip() {
        const tooltipClasses = [
            'tooltip',
            'absolute',
            'left-1/2',
            '-translate-x-1/2',
            'bottom-full',
            'mb-1',
            'text-xs',
            'text-white',
            'bg-black',
            'px-2',
            'py-1',
            'rounded',
            'opacity-0',
            'transition-opacity',
            'duration-300',
            'pointer-events-none',
            'z-10'
        ];

        const style = document.createElement('style');
        style.textContent = `
            .show-tooltip {
            opacity: 1 !important;
            }
        `;
        document.head.appendChild(style);

        document.querySelectorAll('.can-copy').forEach(elem => {
            elem.classList.add('cursor-pointer', 'relative', 'inline-block');

            // Создаем тултип, если его нет
            let tooltip = elem.querySelector('.tooltip');
            if (!tooltip) {
            tooltip = document.createElement('span');
            tooltip.classList.add(...tooltipClasses);
            tooltip.textContent = 'Скопировано';
            elem.appendChild(tooltip);
            }

            elem.addEventListener('click', () => {
            // Временное скрытие тултипа для корректного копирования
            tooltip.style.display = 'none';

            // Получаем только текст без тултипа
            const range = document.createRange();
            range.selectNodeContents(elem);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            const text = selection.toString().trim();

            // Восстанавливаем тултип
            tooltip.style.display = '';

            // Очищаем выделение
            selection.removeAllRanges();

            // Копируем текст
            navigator.clipboard.writeText(text).then(() => {
                tooltip.classList.add('show-tooltip');
                setTimeout(() => tooltip.classList.remove('show-tooltip'), 2000);
            });
            });
        });
        })();
    </script>

    <script>
        let blurTimeout = null;
        let fallbackTimeout = null;
        let appOpened = false;

        function logEvent(msg) {
            // console.log(msg);
            // const logArea = document.getElementById('debug');
            // if (logArea) {
            //     const div = document.createElement('div');
            //     div.textContent = `${new Date().toLocaleTimeString()} — ${msg}`;
            //     logArea.appendChild(div);
            // }
        }

        function tryToOpenDeepLink(event, webLink, deepLink) {
            event.preventDefault();
            appOpened = false;

            logEvent('Trying to open deep link: ' + deepLink);

            // Слушаем blur - возможно, переключение в приложение
            function onBlur() {
                logEvent('blur detected — возможно переключение в нативное приложение');
                // Если в течение 2 секунд не будет focus, считаем, что приложение открылось
                blurTimeout = setTimeout(() => {
                    appOpened = true;
                    logEvent('Приложение, скорее всего, открылось (blur без быстрого focus)');
                    cleanup();
                }, 2000);
            }

            // Слушаем focus - пользователь вернулся в браузер
            function onFocus() {
                logEvent('focus detected — пользователь вернулся в браузер');
                if (blurTimeout) {
                    clearTimeout(blurTimeout);
                    blurTimeout = null;
                }
            }

            // Убираем слушатели и таймеры
            function cleanup() {
                window.removeEventListener('blur', onBlur);
                window.removeEventListener('focus', onFocus);
                if (fallbackTimeout) {
                    clearTimeout(fallbackTimeout);
                    fallbackTimeout = null;
                }
                if (blurTimeout) {
                    clearTimeout(blurTimeout);
                    blurTimeout = null;
                }
            }

            window.addEventListener('blur', onBlur);
            window.addEventListener('focus', onFocus);

            // Таймаут fallback — если приложение не открылось за 5 секунд, переходим по web-ссылке
            fallbackTimeout = setTimeout(() => {
                if (!appOpened) {
                    logEvent('Fallback triggered — открываем web-ссылку: ' + webLink);
                    window.location.href = webLink;
                    cleanup();
                }
            }, 4000);

            // Запускаем deeplink
            window.location.href = deepLink;
        }
    </script>
    <script>
        const logo = document.getElementById('logo');
        let interval, holdTime = 0;
        let isExploding = false;

        // 💥 Запуск дрожания и взрыва
        function startShakeAndExplode() {
        if (isExploding) return;
        isExploding = true;
        holdTime = 0;

        interval = setInterval(() => {
            holdTime += 100;

            const intensity = Math.min(holdTime / 1000, 1) * 4;
            const x = (Math.random() - 0.5) * intensity;
            const y = (Math.random() - 0.5) * intensity;

            logo.style.transform = `translate(${x}px, ${y}px) rotate(${x + y}deg)`;

            if (holdTime >= 3000) {
            clearInterval(interval);
            explodeLogo();
            }
        }, 100);
        }

        // 🖱 ПК: двойной клик
        logo.addEventListener('dblclick', () => {
        startShakeAndExplode();
        });

        // 📱 Мобильные устройства: двойной тап
        let lastTap = 0;
        logo.addEventListener('touchstart', (e) => {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;

        if (tapLength < 400 && tapLength > 0) {
            e.preventDefault();
            startShakeAndExplode();
        }

        lastTap = currentTime;
        });

        // 💥 Взрыв логотипа
        function explodeLogo() {
        const rect = logo.getBoundingClientRect();
        const logoParent = logo.parentElement;

        logo.style.visibility = 'hidden';

        const numFragments = 60;

        for (let i = 0; i < numFragments; i++) {
            const frag = document.createElement('div');
            frag.classList.add('fragment');

            frag.style.left = rect.left + rect.width / 2 + 'px';
            frag.style.top = rect.top + rect.height / 2 + 'px';
            frag.style.position = 'fixed';

            logoParent.appendChild(frag);

            const dx = (Math.random() - 0.5) * 500;
            const dy = (Math.random() - 0.5) * 500;

            frag.animate([
            { transform: 'translate(0, 0)', opacity: 1 },
            { transform: `translate(${dx}px, ${dy}px)`, opacity: 0 }
            ], {
            duration: 1000 + Math.random() * 300,
            easing: 'ease-out',
            fill: 'forwards'
            });

            setTimeout(() => frag.remove(), 2000);
        }

        setTimeout(() => {
            logo.style.visibility = 'visible';
            logo.style.transform = 'none';
            isExploding = false;
        }, 3000);
        }
    </script>

</body>
</html>
